'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  Building2,
  Plus,
  Settings,
  Trash2,
  Eye,
  Search,
  Copy,
  Calendar,
  MapPin,
  User,
  Loader2,
  AlertCircle,
  Lock,
  Mail,
  Shield,
  CheckCircle
} from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [showPasswordDialog, setShowPasswordDialog] = useState(false)
  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: '',
    password_cantiere: '',
    codice_univoco: ''
  })
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  const [passwordMode, setPasswordMode] = useState<'change' | 'recover' | 'view'>('change')
  const [revealedPassword, setRevealedPassword] = useState('')
  const [showRevealedPassword, setShowRevealedPassword] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)
    } catch (error) {
      console.error('Errore nel caricamento cantieri:', error)
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCantiere = async () => {
    try {
      await cantieriApi.createCantiere(formData)
      setShowCreateDialog(false)
      setFormData({
        commessa: '',
        descrizione: '',
        nome_cliente: '',
        indirizzo_cantiere: '',
        citta_cantiere: '',
        nazione_cantiere: '',
        password_cantiere: '',
        codice_univoco: ''
      })
      loadCantieri()
    } catch (error) {
      console.error('Errore nella creazione cantiere:', error)
      setError('Errore nella creazione del cantiere')
    }
  }

  const handleEditCantiere = async () => {
    if (!selectedCantiere) return
    
    try {
      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)
      setShowEditDialog(false)
      setSelectedCantiere(null)
      loadCantieri()
    } catch (error) {
      console.error('Errore nella modifica cantiere:', error)
      setError('Errore nella modifica del cantiere')
    }
  }

  const handleSelectCantiere = (cantiere: Cantiere) => {
    // Salva il cantiere selezionato nel localStorage
    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())
    localStorage.setItem('selectedCantiereName', cantiere.commessa)
    
    // Naviga alla pagina del cantiere specifico
    router.push(`/cantieri/${cantiere.id_cantiere}`)
  }

  const openEditDialog = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere)
    setFormData({
      commessa: cantiere.commessa || '',
      descrizione: cantiere.descrizione || '',
      nome_cliente: cantiere.nome_cliente || '',
      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',
      citta_cantiere: cantiere.citta_cantiere || '',
      nazione_cantiere: cantiere.nazione_cantiere || '',
      password_cantiere: cantiere.password_cantiere || '',
      codice_univoco: cantiere.codice_univoco || ''
    })
    setShowEditDialog(true)
  }

  const handleVerifyAndShowPassword = async () => {
    if (!selectedCantiere || !passwordData.currentPassword) {
      setError('Inserisci la password attuale')
      return
    }

    try {
      setLoading(true)
      // Simula chiamata API per verificare password e mostrarla
      // In realtà dovrebbe chiamare: /api/cantieri/{id}/verify-password
      console.log('Verifica password per cantiere:', selectedCantiere.id_cantiere)

      // Simula successo - in realtà l'API restituirebbe la password decriptata
      setRevealedPassword('password123') // Questa verrebbe dall'API
      setShowRevealedPassword(true)
      setError('')
    } catch (error) {
      console.error('Errore nella verifica password:', error)
      setError('Password non corretta')
    } finally {
      setLoading(false)
    }
  }

  const handleRecoverPassword = async () => {
    if (!selectedCantiere) return

    try {
      setLoading(true)
      // Simula chiamata API per recupero diretto password
      // In realtà dovrebbe chiamare: /api/cantieri/{id}/recover-password
      console.log('Recupero password per cantiere:', selectedCantiere.id_cantiere)

      // Simula successo
      setRevealedPassword('password123') // Questa verrebbe dall'API
      setShowRevealedPassword(true)
      setError('')
    } catch (error) {
      console.error('Errore nel recupero password:', error)
      setError('Impossibile recuperare la password. Potrebbe essere solo hashata.')
    } finally {
      setLoading(false)
    }
  }

  const handleSendPasswordByEmail = async () => {
    if (!selectedCantiere) return

    try {
      setLoading(true)
      // Simula invio email con password
      console.log('Invio password via email per cantiere:', selectedCantiere.id_cantiere)

      // Simula successo
      setError('')
      alert('Password inviata via email all\'amministratore del cantiere')
    } catch (error) {
      console.error('Errore nell\'invio email:', error)
      setError('Errore nell\'invio email')
    } finally {
      setLoading(false)
    }
  }

  const handleChangePassword = async () => {
    if (!selectedCantiere) return

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setError('Le password non coincidono')
      return
    }

    if (!passwordData.currentPassword) {
      setError('Inserisci la password attuale per confermare il cambio')
      return
    }

    try {
      setLoading(true)
      // Simula chiamata API per cambio password
      // In realtà dovrebbe chiamare: /api/cantieri/{id}/change-password
      console.log('Cambio password per cantiere:', selectedCantiere.id_cantiere)

      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      setShowPasswordDialog(false)
      setError('')
      alert('Password cambiata con successo')
    } catch (error) {
      console.error('Errore nel cambio password:', error)
      setError('Errore nel cambio password. Verifica che la password attuale sia corretta.')
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // Visual feedback could be added here (toast notification)
      console.log('Codice copiato:', text)
    } catch (err) {
      console.error('Errore nella copia:', err)
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca cantieri..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Cantiere
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>
              <DialogDescription>
                Inserisci i dettagli del nuovo cantiere
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="commessa" className="text-right">
                  Commessa
                </Label>
                <Input
                  id="commessa"
                  value={formData.commessa}
                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="descrizione" className="text-right">
                  Descrizione
                </Label>
                <Input
                  id="descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="nome_cliente" className="text-right">
                  Cliente
                </Label>
                <Input
                  id="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password_cantiere" className="text-right">
                  Password
                </Label>
                <Input
                  id="password_cantiere"
                  type="password"
                  value={formData.password_cantiere}
                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateCantiere} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">Crea Cantiere</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="mb-4 p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}



      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : filteredCantieri.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nessun cantiere trovato</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}
            </p>
            {!searchTerm && (
              <Button
                onClick={() => setShowCreateDialog(true)}
                className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
              >
                <Plus className="mr-2 h-4 w-4" />
                Crea Primo Cantiere
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Commessa</TableHead>
                <TableHead>Descrizione</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Data Creazione</TableHead>
                <TableHead>Codice</TableHead>
                <TableHead>Password</TableHead>
                <TableHead className="text-right">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCantieri.map((cantiere) => (
                <TableRow key={cantiere.id_cantiere}>
                  <TableCell className="font-medium">{cantiere.commessa}</TableCell>
                  <TableCell>{cantiere.descrizione}</TableCell>
                  <TableCell>{cantiere.nome_cliente}</TableCell>
                  <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {cantiere.codice_univoco}
                    </code>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <code className="text-sm bg-green-100 text-green-800 px-2 py-1 rounded">
                        {cantiere.password_cantiere ? '••••••••' : 'Non impostata'}
                      </code>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-blue-600 hover:bg-blue-50 p-1"
                        title="Gestisci password cantiere"
                        onClick={() => {
                          setSelectedCantiere(cantiere)
                          setShowPasswordDialog(true)
                        }}
                      >
                        <Lock className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button
                        size="sm"
                        onClick={() => openEditDialog(cantiere)}
                        className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                        title="Modifica cantiere"
                      >
                        <Settings className="h-3 w-3" />
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => handleSelectCantiere(cantiere)}
                        className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                      >
                        Gestisci
                        <Eye className="ml-2 h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Dialog di modifica */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Modifica Cantiere</DialogTitle>
            <DialogDescription>
              Modifica i dettagli del cantiere selezionato
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-commessa" className="text-right">
                Commessa
              </Label>
              <Input
                id="edit-commessa"
                value={formData.commessa}
                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-descrizione" className="text-right">
                Descrizione
              </Label>
              <Input
                id="edit-descrizione"
                value={formData.descrizione}
                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nome_cliente" className="text-right">
                Cliente
              </Label>
              <Input
                id="edit-nome_cliente"
                value={formData.nome_cliente}
                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-indirizzo_cantiere" className="text-right">
                Indirizzo
              </Label>
              <Input
                id="edit-indirizzo_cantiere"
                value={formData.indirizzo_cantiere}
                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-citta_cantiere" className="text-right">
                Città
              </Label>
              <Input
                id="edit-citta_cantiere"
                value={formData.citta_cantiere}
                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nazione_cantiere" className="text-right">
                Nazione
              </Label>
              <Input
                id="edit-nazione_cantiere"
                value={formData.nazione_cantiere}
                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-password_cantiere" className="text-right">
                Password
              </Label>
              <Input
                id="edit-password_cantiere"
                type="password"
                value={formData.password_cantiere}
                onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowEditDialog(false)} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">
              Annulla
            </Button>
            <Button onClick={handleEditCantiere} className="relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]">Salva Modifiche</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog gestione password ottimale */}
      <Dialog open={showPasswordDialog} onOpenChange={(open) => {
        setShowPasswordDialog(open)
        if (!open) {
          setPasswordMode('change')
          setShowRevealedPassword(false)
          setRevealedPassword('')
          setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' })
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Lock className="h-5 w-5" />
              Gestione Password - {selectedCantiere?.commessa}
            </DialogTitle>
            <DialogDescription>
              Scegli come gestire la password del cantiere
            </DialogDescription>
          </DialogHeader>

          {/* Tabs per le diverse modalità */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            <button
              onClick={() => setPasswordMode('view')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                passwordMode === 'view'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Eye className="inline mr-2 h-4 w-4" />
              Visualizza
            </button>
            <button
              onClick={() => setPasswordMode('change')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                passwordMode === 'change'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Settings className="inline mr-2 h-4 w-4" />
              Cambia
            </button>
            <button
              onClick={() => setPasswordMode('recover')}
              className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
                passwordMode === 'recover'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Mail className="inline mr-2 h-4 w-4" />
              Recupera
            </button>
          </div>

          <div className="space-y-6">
            {/* Modalità Visualizza Password */}
            {passwordMode === 'view' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Visualizza Password Attuale
                </h3>
                <p className="text-sm text-gray-600">
                  Inserisci la password attuale per visualizzarla in chiaro
                </p>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="verify-password">Password Attuale</Label>
                    <Input
                      id="verify-password"
                      type="password"
                      placeholder="Inserisci la password per verificarla"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                    />
                  </div>
                  {showRevealedPassword && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-800">Password Verificata</span>
                      </div>
                      <div className="bg-white p-3 rounded border">
                        <code className="text-lg font-mono">{revealedPassword}</code>
                      </div>
                    </div>
                  )}
                  <Button
                    onClick={handleVerifyAndShowPassword}
                    disabled={loading || !passwordData.currentPassword}
                    className="w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Shield className="mr-2 h-4 w-4" />}
                    Verifica e Mostra Password
                  </Button>
                </div>
              </div>
            )}

            {/* Modalità Cambia Password */}
            {passwordMode === 'change' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Cambia Password
                </h3>
                <p className="text-sm text-gray-600">
                  Inserisci la password attuale e la nuova password
                </p>
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="current-password-change">Password Attuale</Label>
                    <Input
                      id="current-password-change"
                      type="password"
                      placeholder="Password attuale per conferma"
                      value={passwordData.currentPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-password">Nuova Password</Label>
                    <Input
                      id="new-password"
                      type="password"
                      placeholder="Inserisci la nuova password"
                      value={passwordData.newPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="confirm-password">Conferma Nuova Password</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      placeholder="Conferma la nuova password"
                      value={passwordData.confirmPassword}
                      onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                    />
                  </div>
                  <Button
                    onClick={handleChangePassword}
                    disabled={loading || !passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword}
                    className="w-full relative overflow-hidden bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-blue-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                  >
                    {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Settings className="mr-2 h-4 w-4" />}
                    Cambia Password
                  </Button>
                </div>
              </div>
            )}

            {/* Modalità Recupera Password */}
            {passwordMode === 'recover' && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  Recupera Password
                </h3>
                <p className="text-sm text-gray-600">
                  Opzioni per recuperare una password dimenticata
                </p>

                <div className="space-y-4">
                  {/* Recupero diretto */}
                  <div className="p-4 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-800 mb-2">Recupero Diretto</h4>
                    <p className="text-sm text-blue-700 mb-3">
                      Tenta di recuperare la password dal sistema (funziona solo se la password è stata salvata in formato recuperabile)
                    </p>
                    {showRevealedPassword && (
                      <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded">
                        <div className="flex items-center gap-2 mb-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="font-medium text-green-800">Password Recuperata</span>
                        </div>
                        <code className="text-lg font-mono bg-white p-2 rounded border block">{revealedPassword}</code>
                      </div>
                    )}
                    <Button
                      onClick={handleRecoverPassword}
                      disabled={loading}
                      className="w-full relative overflow-hidden bg-orange-600 hover:bg-orange-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-orange-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                    >
                      {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Shield className="mr-2 h-4 w-4" />}
                      Recupera Password
                    </Button>
                  </div>

                  {/* Invio via email */}
                  <div className="p-4 border border-green-200 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-2">Invio via Email</h4>
                    <p className="text-sm text-green-700 mb-3">
                      Invia la password all'indirizzo email dell'amministratore del cantiere
                    </p>
                    <Button
                      onClick={handleSendPasswordByEmail}
                      disabled={loading}
                      className="w-full relative overflow-hidden bg-green-600 hover:bg-green-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg hover:shadow-green-500/25 before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] before:transition-transform before:duration-700 before:ease-in-out hover:before:translate-x-[100%]"
                    >
                      {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Mail className="mr-2 h-4 w-4" />}
                      Invia Password via Email
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Messaggio di errore */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-800">Errore</span>
                </div>
                <p className="text-sm text-red-700 mt-1">{error}</p>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPasswordDialog(false)}
            >
              Chiudi
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
