'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { useAuth } from '@/contexts/AuthContext'
import { cantieriApi } from '@/lib/api'
import { Cantiere } from '@/types'
import {
  Building2,
  Plus,
  Edit,
  Trash2,
  Eye,
  Search,
  Copy,
  Calendar,
  MapPin,
  User,
  Loader2,
  AlertCircle
} from 'lucide-react'

export default function CantieriPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [cantieri, setCantieri] = useState<Cantiere[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)
  const [formData, setFormData] = useState({
    commessa: '',
    descrizione: '',
    nome_cliente: '',
    indirizzo_cantiere: '',
    citta_cantiere: '',
    nazione_cantiere: '',
    password_cantiere: '',
    codice_univoco: ''
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      loadCantieri()
    }
  }, [isAuthenticated])

  const loadCantieri = async () => {
    try {
      setLoading(true)
      const data = await cantieriApi.getCantieri()
      setCantieri(data)
    } catch (error) {
      console.error('Errore nel caricamento cantieri:', error)
      setError('Errore nel caricamento dei cantieri')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCantiere = async () => {
    try {
      await cantieriApi.createCantiere(formData)
      setShowCreateDialog(false)
      setFormData({
        commessa: '',
        descrizione: '',
        nome_cliente: '',
        indirizzo_cantiere: '',
        citta_cantiere: '',
        nazione_cantiere: '',
        password_cantiere: '',
        codice_univoco: ''
      })
      loadCantieri()
    } catch (error) {
      console.error('Errore nella creazione cantiere:', error)
      setError('Errore nella creazione del cantiere')
    }
  }

  const handleEditCantiere = async () => {
    if (!selectedCantiere) return
    
    try {
      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)
      setShowEditDialog(false)
      setSelectedCantiere(null)
      loadCantieri()
    } catch (error) {
      console.error('Errore nella modifica cantiere:', error)
      setError('Errore nella modifica del cantiere')
    }
  }

  const handleSelectCantiere = (cantiere: Cantiere) => {
    // Salva il cantiere selezionato nel localStorage
    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())
    localStorage.setItem('selectedCantiereName', cantiere.commessa)
    
    // Naviga alla pagina del cantiere specifico
    router.push(`/cantieri/${cantiere.id_cantiere}`)
  }

  const openEditDialog = (cantiere: Cantiere) => {
    setSelectedCantiere(cantiere)
    setFormData({
      commessa: cantiere.commessa || '',
      descrizione: cantiere.descrizione || '',
      nome_cliente: cantiere.nome_cliente || '',
      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',
      citta_cantiere: cantiere.citta_cantiere || '',
      nazione_cantiere: cantiere.nazione_cantiere || '',
      password_cantiere: cantiere.password_cantiere || '',
      codice_univoco: cantiere.codice_univoco || ''
    })
    setShowEditDialog(true)
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // Visual feedback could be added here (toast notification)
      console.log('Codice copiato:', text)
    } catch (err) {
      console.error('Errore nella copia:', err)
    }
  }

  const filteredCantieri = cantieri.filter(cantiere =>
    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <div className="relative w-80">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Cerca cantieri..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 w-full"
            />
          </div>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button className="btn-primary">
              <Plus className="mr-2 h-4 w-4" />
              Nuovo Cantiere
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>
              <DialogDescription>
                Inserisci i dettagli del nuovo cantiere
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="commessa" className="text-right">
                  Commessa
                </Label>
                <Input
                  id="commessa"
                  value={formData.commessa}
                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="descrizione" className="text-right">
                  Descrizione
                </Label>
                <Input
                  id="descrizione"
                  value={formData.descrizione}
                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="nome_cliente" className="text-right">
                  Cliente
                </Label>
                <Input
                  id="nome_cliente"
                  value={formData.nome_cliente}
                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="password_cantiere" className="text-right">
                  Password
                </Label>
                <Input
                  id="password_cantiere"
                  type="password"
                  value={formData.password_cantiere}
                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateCantiere}>Crea Cantiere</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {error && (
        <div className="mb-4 p-4 border border-red-200 rounded-lg bg-red-50">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}



      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : filteredCantieri.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nessun cantiere trovato</h3>
            <p className="text-muted-foreground text-center mb-4">
              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}
            </p>
            {!searchTerm && (
              <Button
                onClick={() => setShowCreateDialog(true)}
                className="btn-primary"
              >
                <Plus className="mr-2 h-4 w-4 btn-icon" />
                Crea Primo Cantiere
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Commessa</TableHead>
                <TableHead>Descrizione</TableHead>
                <TableHead>Cliente</TableHead>
                <TableHead>Data Creazione</TableHead>
                <TableHead>Codice</TableHead>
                <TableHead className="text-right">Azioni</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredCantieri.map((cantiere) => (
                <TableRow key={cantiere.id_cantiere}>
                  <TableCell className="font-medium">{cantiere.commessa}</TableCell>
                  <TableCell>{cantiere.descrizione}</TableCell>
                  <TableCell>{cantiere.nome_cliente}</TableCell>
                  <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <code className="text-sm bg-muted px-2 py-1 rounded">
                        {cantiere.codice_univoco}
                      </code>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(cantiere.codice_univoco)}
                        className="btn-quick hover:bg-slate-100 hover:text-slate-900 transition-all duration-300 ease-in-out"
                        title="Copia codice univoco"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSelectCantiere(cantiere)}
                        className="btn-outline btn-sm"
                      >
                        <Eye className="mr-2 h-3 w-3 btn-icon" />
                        Gestisci
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditDialog(cantiere)}
                        className="btn-quick hover:bg-slate-100 hover:text-slate-900 transition-all duration-300 ease-in-out"
                        title="Modifica cantiere"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {/* Dialog di modifica */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Modifica Cantiere</DialogTitle>
            <DialogDescription>
              Modifica i dettagli del cantiere selezionato
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-commessa" className="text-right">
                Commessa
              </Label>
              <Input
                id="edit-commessa"
                value={formData.commessa}
                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-descrizione" className="text-right">
                Descrizione
              </Label>
              <Input
                id="edit-descrizione"
                value={formData.descrizione}
                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nome_cliente" className="text-right">
                Cliente
              </Label>
              <Input
                id="edit-nome_cliente"
                value={formData.nome_cliente}
                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-indirizzo_cantiere" className="text-right">
                Indirizzo
              </Label>
              <Input
                id="edit-indirizzo_cantiere"
                value={formData.indirizzo_cantiere}
                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-citta_cantiere" className="text-right">
                Città
              </Label>
              <Input
                id="edit-citta_cantiere"
                value={formData.citta_cantiere}
                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-nazione_cantiere" className="text-right">
                Nazione
              </Label>
              <Input
                id="edit-nazione_cantiere"
                value={formData.nazione_cantiere}
                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Annulla
            </Button>
            <Button onClick={handleEditCantiere}>Salva Modifiche</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
