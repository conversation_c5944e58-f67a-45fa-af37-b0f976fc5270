{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/cantieri/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { cantieriApi } from '@/lib/api'\nimport { Cantiere } from '@/types'\nimport {\n  Building2,\n  Plus,\n  Edit,\n  Trash2,\n  Eye,\n  Search,\n  Copy,\n  Calendar,\n  MapPin,\n  User,\n  Loader2,\n  AlertCircle\n} from 'lucide-react'\n\nexport default function CantieriPage() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n  const [cantieri, setCantieri] = useState<Cantiere[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState('')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showEditDialog, setShowEditDialog] = useState(false)\n  const [selectedCantiere, setSelectedCantiere] = useState<Cantiere | null>(null)\n  const [formData, setFormData] = useState({\n    commessa: '',\n    descrizione: '',\n    nome_cliente: '',\n    indirizzo_cantiere: '',\n    citta_cantiere: '',\n    nazione_cantiere: '',\n    password_cantiere: '',\n    codice_univoco: ''\n  })\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push('/login')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      loadCantieri()\n    }\n  }, [isAuthenticated])\n\n  const loadCantieri = async () => {\n    try {\n      setLoading(true)\n      const data = await cantieriApi.getCantieri()\n      setCantieri(data)\n    } catch (error) {\n      console.error('Errore nel caricamento cantieri:', error)\n      setError('Errore nel caricamento dei cantieri')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleCreateCantiere = async () => {\n    try {\n      await cantieriApi.createCantiere(formData)\n      setShowCreateDialog(false)\n      setFormData({\n        commessa: '',\n        descrizione: '',\n        nome_cliente: '',\n        indirizzo_cantiere: '',\n        citta_cantiere: '',\n        nazione_cantiere: '',\n        password_cantiere: '',\n        codice_univoco: ''\n      })\n      loadCantieri()\n    } catch (error) {\n      console.error('Errore nella creazione cantiere:', error)\n      setError('Errore nella creazione del cantiere')\n    }\n  }\n\n  const handleEditCantiere = async () => {\n    if (!selectedCantiere) return\n    \n    try {\n      await cantieriApi.updateCantiere(selectedCantiere.id_cantiere, formData)\n      setShowEditDialog(false)\n      setSelectedCantiere(null)\n      loadCantieri()\n    } catch (error) {\n      console.error('Errore nella modifica cantiere:', error)\n      setError('Errore nella modifica del cantiere')\n    }\n  }\n\n  const handleSelectCantiere = (cantiere: Cantiere) => {\n    // Salva il cantiere selezionato nel localStorage\n    localStorage.setItem('selectedCantiereId', cantiere.id_cantiere.toString())\n    localStorage.setItem('selectedCantiereName', cantiere.commessa)\n    \n    // Naviga alla pagina del cantiere specifico\n    router.push(`/cantieri/${cantiere.id_cantiere}`)\n  }\n\n  const openEditDialog = (cantiere: Cantiere) => {\n    setSelectedCantiere(cantiere)\n    setFormData({\n      commessa: cantiere.commessa || '',\n      descrizione: cantiere.descrizione || '',\n      nome_cliente: cantiere.nome_cliente || '',\n      indirizzo_cantiere: cantiere.indirizzo_cantiere || '',\n      citta_cantiere: cantiere.citta_cantiere || '',\n      nazione_cantiere: cantiere.nazione_cantiere || '',\n      password_cantiere: cantiere.password_cantiere || '',\n      codice_univoco: cantiere.codice_univoco || ''\n    })\n    setShowEditDialog(true)\n  }\n\n  const copyToClipboard = async (text: string) => {\n    try {\n      await navigator.clipboard.writeText(text)\n      // Visual feedback could be added here (toast notification)\n      console.log('Codice copiato:', text)\n    } catch (err) {\n      console.error('Errore nella copia:', err)\n    }\n  }\n\n  const filteredCantieri = cantieri.filter(cantiere =>\n    cantiere.commessa.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.descrizione?.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    cantiere.nome_cliente?.toLowerCase().includes(searchTerm.toLowerCase())\n  )\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <Loader2 className=\"h-8 w-8 animate-spin\" />\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-4\">\n          <div className=\"relative w-80\">\n            <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n            <Input\n              placeholder=\"Cerca cantieri...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"pl-8 w-full\"\n            />\n          </div>\n        </div>\n        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n          <DialogTrigger asChild>\n            <Button className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">\n              <Plus className=\"mr-2 h-4 w-4\" />\n              Nuovo Cantiere\n            </Button>\n          </DialogTrigger>\n          <DialogContent className=\"sm:max-w-[425px]\">\n            <DialogHeader>\n              <DialogTitle>Crea Nuovo Cantiere</DialogTitle>\n              <DialogDescription>\n                Inserisci i dettagli del nuovo cantiere\n              </DialogDescription>\n            </DialogHeader>\n            <div className=\"grid gap-4 py-4\">\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"commessa\" className=\"text-right\">\n                  Commessa\n                </Label>\n                <Input\n                  id=\"commessa\"\n                  value={formData.commessa}\n                  onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"descrizione\" className=\"text-right\">\n                  Descrizione\n                </Label>\n                <Input\n                  id=\"descrizione\"\n                  value={formData.descrizione}\n                  onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"nome_cliente\" className=\"text-right\">\n                  Cliente\n                </Label>\n                <Input\n                  id=\"nome_cliente\"\n                  value={formData.nome_cliente}\n                  onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n              <div className=\"grid grid-cols-4 items-center gap-4\">\n                <Label htmlFor=\"password_cantiere\" className=\"text-right\">\n                  Password\n                </Label>\n                <Input\n                  id=\"password_cantiere\"\n                  type=\"password\"\n                  value={formData.password_cantiere}\n                  onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}\n                  className=\"col-span-3\"\n                />\n              </div>\n            </div>\n            <DialogFooter>\n              <Button onClick={handleCreateCantiere} className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">Crea Cantiere</Button>\n            </DialogFooter>\n          </DialogContent>\n        </Dialog>\n      </div>\n\n      {error && (\n        <div className=\"mb-4 p-4 border border-red-200 rounded-lg bg-red-50\">\n          <div className=\"flex items-center\">\n            <AlertCircle className=\"h-4 w-4 text-red-600 mr-2\" />\n            <span className=\"text-red-800\">{error}</span>\n          </div>\n        </div>\n      )}\n\n\n\n      {loading ? (\n        <div className=\"flex items-center justify-center py-8\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n        </div>\n      ) : filteredCantieri.length === 0 ? (\n        <Card>\n          <CardContent className=\"flex flex-col items-center justify-center py-8\">\n            <Building2 className=\"h-12 w-12 text-muted-foreground mb-4\" />\n            <h3 className=\"text-lg font-semibold mb-2\">Nessun cantiere trovato</h3>\n            <p className=\"text-muted-foreground text-center mb-4\">\n              {searchTerm ? 'Nessun cantiere corrisponde ai criteri di ricerca' : 'Crea il tuo primo cantiere per iniziare'}\n            </p>\n            {!searchTerm && (\n              <Button\n                onClick={() => setShowCreateDialog(true)}\n                className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\"\n              >\n                <Plus className=\"mr-2 h-4 w-4\" />\n                Crea Primo Cantiere\n              </Button>\n            )}\n          </CardContent>\n        </Card>\n      ) : (\n        <Card>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Commessa</TableHead>\n                <TableHead>Descrizione</TableHead>\n                <TableHead>Cliente</TableHead>\n                <TableHead>Data Creazione</TableHead>\n                <TableHead>Codice</TableHead>\n                <TableHead>Password</TableHead>\n                <TableHead className=\"text-right\">Azioni</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {filteredCantieri.map((cantiere) => (\n                <TableRow key={cantiere.id_cantiere}>\n                  <TableCell className=\"font-medium\">{cantiere.commessa}</TableCell>\n                  <TableCell>{cantiere.descrizione}</TableCell>\n                  <TableCell>{cantiere.nome_cliente}</TableCell>\n                  <TableCell>{new Date(cantiere.data_creazione).toLocaleDateString()}</TableCell>\n                  <TableCell>\n                    <code className=\"text-sm bg-muted px-2 py-1 rounded\">\n                      {cantiere.codice_univoco}\n                    </code>\n                  </TableCell>\n                  <TableCell>\n                    <code className=\"text-sm bg-muted px-2 py-1 rounded\">\n                      {cantiere.password_cantiere || 'Non impostata'}\n                    </code>\n                  </TableCell>\n                  <TableCell className=\"text-right\">\n                    <div className=\"flex items-center justify-end space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        onClick={() => handleSelectCantiere(cantiere)}\n                        className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\"\n                      >\n                        Gestisci\n                        <Eye className=\"ml-2 h-3 w-3\" />\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        onClick={() => openEditDialog(cantiere)}\n                        className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 text-sm rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\"\n                        title=\"Modifica cantiere\"\n                      >\n                        <Edit className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </Card>\n      )}\n\n      {/* Dialog di modifica */}\n      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>\n        <DialogContent className=\"sm:max-w-[425px]\">\n          <DialogHeader>\n            <DialogTitle>Modifica Cantiere</DialogTitle>\n            <DialogDescription>\n              Modifica i dettagli del cantiere selezionato\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"grid gap-4 py-4\">\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-commessa\" className=\"text-right\">\n                Commessa\n              </Label>\n              <Input\n                id=\"edit-commessa\"\n                value={formData.commessa}\n                onChange={(e) => setFormData({ ...formData, commessa: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-descrizione\" className=\"text-right\">\n                Descrizione\n              </Label>\n              <Input\n                id=\"edit-descrizione\"\n                value={formData.descrizione}\n                onChange={(e) => setFormData({ ...formData, descrizione: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nome_cliente\" className=\"text-right\">\n                Cliente\n              </Label>\n              <Input\n                id=\"edit-nome_cliente\"\n                value={formData.nome_cliente}\n                onChange={(e) => setFormData({ ...formData, nome_cliente: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-indirizzo_cantiere\" className=\"text-right\">\n                Indirizzo\n              </Label>\n              <Input\n                id=\"edit-indirizzo_cantiere\"\n                value={formData.indirizzo_cantiere}\n                onChange={(e) => setFormData({ ...formData, indirizzo_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-citta_cantiere\" className=\"text-right\">\n                Città\n              </Label>\n              <Input\n                id=\"edit-citta_cantiere\"\n                value={formData.citta_cantiere}\n                onChange={(e) => setFormData({ ...formData, citta_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-nazione_cantiere\" className=\"text-right\">\n                Nazione\n              </Label>\n              <Input\n                id=\"edit-nazione_cantiere\"\n                value={formData.nazione_cantiere}\n                onChange={(e) => setFormData({ ...formData, nazione_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n            <div className=\"grid grid-cols-4 items-center gap-4\">\n              <Label htmlFor=\"edit-password_cantiere\" className=\"text-right\">\n                Password\n              </Label>\n              <Input\n                id=\"edit-password_cantiere\"\n                type=\"password\"\n                value={formData.password_cantiere}\n                onChange={(e) => setFormData({ ...formData, password_cantiere: e.target.value })}\n                className=\"col-span-3\"\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button onClick={() => setShowEditDialog(false)} className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">\n              Annulla\n            </Button>\n            <Button onClick={handleEditCantiere} className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-300 ease-in-out hover:shadow-lg\">Salva Modifiche</Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;;;;AA6Be,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,cAAc;QACd,oBAAoB;QACpB,gBAAgB;QAChB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,OAAO,IAAI,CAAC;YACd;QACF;iCAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB;gBACnB;YACF;QACF;iCAAG;QAAC;KAAgB;IAEpB,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oHAAA,CAAA,cAAW,CAAC,WAAW;YAC1C,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC;YACjC,oBAAoB;YACpB,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,oBAAoB;gBACpB,gBAAgB;gBAChB,kBAAkB;gBAClB,mBAAmB;gBACnB,gBAAgB;YAClB;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,oHAAA,CAAA,cAAW,CAAC,cAAc,CAAC,iBAAiB,WAAW,EAAE;YAC/D,kBAAkB;YAClB,oBAAoB;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,SAAS;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,iDAAiD;QACjD,aAAa,OAAO,CAAC,sBAAsB,SAAS,WAAW,CAAC,QAAQ;QACxE,aAAa,OAAO,CAAC,wBAAwB,SAAS,QAAQ;QAE9D,4CAA4C;QAC5C,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,SAAS,WAAW,EAAE;IACjD;IAEA,MAAM,iBAAiB,CAAC;QACtB,oBAAoB;QACpB,YAAY;YACV,UAAU,SAAS,QAAQ,IAAI;YAC/B,aAAa,SAAS,WAAW,IAAI;YACrC,cAAc,SAAS,YAAY,IAAI;YACvC,oBAAoB,SAAS,kBAAkB,IAAI;YACnD,gBAAgB,SAAS,cAAc,IAAI;YAC3C,kBAAkB,SAAS,gBAAgB,IAAI;YAC/C,mBAAmB,SAAS,iBAAiB,IAAI;YACjD,gBAAgB,SAAS,cAAc,IAAI;QAC7C;QACA,kBAAkB;IACpB;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,2DAA2D;YAC3D,QAAQ,GAAG,CAAC,mBAAmB;QACjC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,WACvC,SAAS,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC/D,SAAS,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW,OACnE,SAAS,YAAY,EAAE,cAAc,SAAS,WAAW,WAAW;IAGtE,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;;;;;;IAGzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC,oIAAA,CAAA,QAAK;oCACJ,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;;;;;;;;;;;;;;;;;kCAIhB,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAM;wBAAkB,cAAc;;0CAC5C,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAIrC,6LAAC,qIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,6LAAC,qIAAA,CAAA,eAAY;;0DACX,6LAAC,qIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,6LAAC,qIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAW,WAAU;kEAAa;;;;;;kEAGjD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACrE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;kEAAa;;;;;;kEAGpD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,WAAW;wDAC3B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACxE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAe,WAAU;kEAAa;;;;;;kEAGrD,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,OAAO,SAAS,YAAY;wDAC5B,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACzE,WAAU;;;;;;;;;;;;0DAGd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAoB,WAAU;kEAAa;;;;;;kEAG1D,6LAAC,oIAAA,CAAA,QAAK;wDACJ,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,iBAAiB;wDACjC,UAAU,CAAC,IAAM,YAAY;gEAAE,GAAG,QAAQ;gEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;4DAAC;wDAC9E,WAAU;;;;;;;;;;;;;;;;;;kDAIhB,6LAAC,qIAAA,CAAA,eAAY;kDACX,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAsB,WAAU;sDAAoI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM5L,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAgB;;;;;;;;;;;;;;;;;YAOrC,wBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;uBAEnB,iBAAiB,MAAM,KAAK,kBAC9B,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,6LAAC;4BAAE,WAAU;sCACV,aAAa,sDAAsD;;;;;;wBAErE,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAOzC,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAa;;;;;;;;;;;;;;;;;sCAGtC,6LAAC,oIAAA,CAAA,YAAS;sCACP,iBAAiB,GAAG,CAAC,CAAC,yBACrB,6LAAC,oIAAA,CAAA,WAAQ;;sDACP,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAe,SAAS,QAAQ;;;;;;sDACrD,6LAAC,oIAAA,CAAA,YAAS;sDAAE,SAAS,WAAW;;;;;;sDAChC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,SAAS,YAAY;;;;;;sDACjC,6LAAC,oIAAA,CAAA,YAAS;sDAAE,IAAI,KAAK,SAAS,cAAc,EAAE,kBAAkB;;;;;;sDAChE,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;gDAAK,WAAU;0DACb,SAAS,cAAc;;;;;;;;;;;sDAG5B,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC;gDAAK,WAAU;0DACb,SAAS,iBAAiB,IAAI;;;;;;;;;;;sDAGnC,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDACnB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,qBAAqB;wDACpC,WAAU;;4DACX;0EAEC,6LAAC,mMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;;;;;;;kEAEjB,6LAAC,qIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,IAAM,eAAe;wDAC9B,WAAU;wDACV,OAAM;kEAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA/BT,SAAS,WAAW;;;;;;;;;;;;;;;;;;;;;0BA2C7C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;0BAC1C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAgB,WAAU;sDAAa;;;;;;sDAGtD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAmB,WAAU;sDAAa;;;;;;sDAGzD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,WAAW;4CAC3B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACxE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAoB,WAAU;sDAAa;;;;;;sDAG1D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,YAAY;4CAC5B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,cAAc,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACzE,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAA0B,WAAU;sDAAa;;;;;;sDAGhE,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,kBAAkB;4CAClC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAsB,WAAU;sDAAa;;;;;;sDAG5D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,cAAc;4CAC9B,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC3E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAwB,WAAU;sDAAa;;;;;;sDAG9D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,SAAS,gBAAgB;4CAChC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC7E,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAyB,WAAU;sDAAa;;;;;;sDAG/D,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,SAAS,iBAAiB;4CACjC,UAAU,CAAC,IAAM,YAAY;oDAAE,GAAG,QAAQ;oDAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC9E,WAAU;;;;;;;;;;;;;;;;;;sCAIhB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,kBAAkB;oCAAQ,WAAU;8CAAoI;;;;;;8CAG/L,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAoB,WAAU;8CAAoI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM/L;GAlZwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}