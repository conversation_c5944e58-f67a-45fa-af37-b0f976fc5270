{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "eaad7a8d865e1cdcc3b56667ff53f0b4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "cbd398e217b66dba255b5d1fda33dea441c545578fc4835956353a1195448d74", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1d70983fce1d6670a7c7620e6fc6055bbbc6188c8f501220ee1c1ce7f1ef3302"}}}, "sortedMiddleware": ["/"], "functions": {}}